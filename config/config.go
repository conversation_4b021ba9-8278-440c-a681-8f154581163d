package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"program-manager/types"
)

// ConfigManager provides generic configuration loading and saving functionality
type ConfigManager[T any] struct {
	filename       string
	defaultFactory func() T
	validator      func(T) error
}

// NewConfigManager creates a new configuration manager for type T
func NewConfigManager[T any](filename string, defaultFactory func() T, validator func(T) error) *ConfigManager[T] {
	return &ConfigManager[T]{
		filename:       filename,
		defaultFactory: defaultFactory,
		validator:      validator,
	}
}

// LoadOrCreate loads configuration from file, creates default if file doesn't exist
func (cm *ConfigManager[T]) LoadOrCreate() (T, error) {
	var config T

	// Try to load existing configuration
	if loadedConfig, err := cm.Load(); err == nil {
		return loadedConfig, nil
	}

	// Create default configuration if loading failed
	if cm.defaultFactory != nil {
		config = cm.defaultFactory()
		log.Printf("No existing config found at %s, created default configuration", cm.filename)
	} else {
		return config, fmt.Errorf("no default factory provided and failed to load %s", cm.filename)
	}

	// Validate default configuration
	if cm.validator != nil {
		if err := cm.validator(config); err != nil {
			return config, fmt.Errorf("default configuration validation failed: %v", err)
		}
	}

	// Save default configuration
	if err := cm.Save(config); err != nil {
		return config, fmt.Errorf("failed to save default configuration: %v", err)
	}

	return config, nil
}

// LoadOnly loads configuration from file, returns error if file doesn't exist
func (cm *ConfigManager[T]) LoadOnly() (T, error) {
	return cm.Load()
}

// Load loads configuration from file
func (cm *ConfigManager[T]) Load() (T, error) {
	var config T

	file, err := os.Open(cm.filename)
	if err != nil {
		return config, err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return config, err
	}

	// Validate loaded configuration
	if cm.validator != nil {
		if err := cm.validator(config); err != nil {
			return config, fmt.Errorf("configuration validation failed: %v", err)
		}
	}

	return config, nil
}

// Save saves configuration to file
func (cm *ConfigManager[T]) Save(config T) error {
	// Validate before saving
	if cm.validator != nil {
		if err := cm.validator(config); err != nil {
			return fmt.Errorf("configuration validation failed: %v", err)
		}
	}

	file, err := os.Create(cm.filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "    ")
	return encoder.Encode(config)
}

// LoadDiurnalConfig loads enhanced diurnal configuration from file
func LoadDiurnalConfig(filename string) (types.DiurnalSetpointConfig, error) {
	var config types.DiurnalSetpointConfig

	file, err := os.Open(filename)
	if err != nil {
		return config, err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return config, err
	}

	return config, nil
}

// SaveDiurnalConfig saves enhanced diurnal configuration to file
func SaveDiurnalConfig(filename string, config types.DiurnalSetpointConfig) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "    ")
	return encoder.Encode(config)
}

// SaveProgramManager saves program manager configuration to file
func SaveProgramManager(filename string, programManager map[string]interface{}) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "    ")
	return encoder.Encode(programManager)
}

// SaveCEBConfig saves CEB configuration to file
func SaveCEBConfig(filename string, config types.CEBConfig) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "    ")
	return encoder.Encode(config)
}

// LoadClockConfig loads Clock configuration from file
func LoadClockConfig(filename string) (types.ClockConfig, error) {
	var config types.ClockConfig

	file, err := os.Open(filename)
	if err != nil {
		return config, err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return config, err
	}

	return config, nil
}

// SaveClockConfig saves Clock configuration to file
func SaveClockConfig(filename string, config types.ClockConfig) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "    ")
	return encoder.Encode(config)
}

// CreateDefaultClockConfig creates a default Clock configuration
func CreateDefaultClockConfig() types.ClockConfig {
	return types.ClockConfig{
		ProgramName:    "Clock Program",
		Enabled:        true,
		Latitude:       17.407104033722273, // Hyderabad coordinates as default
		Longitude:      78.38716849147556,  // Hyderabad coordinates as default
		Timezone:       "Asia/Kolkata",
		NTPServer:      "pool.ntp.org",
		UpdateInterval: 10, // Update every 10 seconds for better visibility
		RedisTopics: types.ClockRedisTopics{
			CurrentTime: "hub:1:clock:currentTime",
			Date:        "hub:1:clock:date",
			DayOfWeek:   "hub:1:clock:dayOfWeek",
			Dawn:        "hub:1:clock:dawn",
			Dusk:        "hub:1:clock:dusk",
			IsDaytime:   "hub:1:clock:isDaytime",
		},
	}
}

// ValidateTimeFormat validates time format
func ValidateTimeFormat(timeStr string) error {
	_, err := time.Parse("15:04", timeStr)
	return err
}

// ValidateInstancePeriodOverlaps validates period overlaps within an instance
func ValidateInstancePeriodOverlaps(instance types.DiurnalInstance) error {
	if !instance.Enabled || len(instance.Periods) == 0 {
		return nil
	}

	periods := instance.Periods
	for i := 0; i < len(periods)-1; i++ {
		for j := i + 1; j < len(periods); j++ {
			// Check if periods have any common days
			hasCommonDay := false
			for day := range periods[i].ActiveDays {
				if periods[i].ActiveDays[day] && periods[j].ActiveDays[day] {
					hasCommonDay = true
					break
				}
			}

			if hasCommonDay {
				// Skip validation for relative times - they will be resolved at runtime
				if IsRelativeTime(periods[i].StartTime) || IsRelativeTime(periods[i].EndTime) ||
					IsRelativeTime(periods[j].StartTime) || IsRelativeTime(periods[j].EndTime) {
					continue
				}

				startA, _ := time.Parse("15:04", periods[i].StartTime)
				endA, _ := time.Parse("15:04", periods[i].EndTime)
				startB, _ := time.Parse("15:04", periods[j].StartTime)
				endB, _ := time.Parse("15:04", periods[j].EndTime)

				// Check if periods overlap or are adjacent
				if (startA.Before(endB) || startA.Equal(endB)) &&
					(endA.After(startB) || endA.Equal(startB)) {
					return errors.New("instance " + instance.InstanceId +
						": periods " + periods[i].PeriodId + " and " +
						periods[j].PeriodId + " overlap or are adjacent")
				}
			}
		}
	}
	return nil
}

// IsRelativeTime checks if a time string contains relative references
func IsRelativeTime(timeStr string) bool {
	return strings.Contains(timeStr, "beforeDawn") ||
		strings.Contains(timeStr, "afterDawn") ||
		strings.Contains(timeStr, "beforeDusk") ||
		strings.Contains(timeStr, "afterDusk")
}

// ValidateLoadedConfig validates loaded enhanced configuration
func ValidateLoadedConfig(config types.DiurnalSetpointConfig) error {
	// Check number of instances
	if len(config.Instances) == 0 {
		return errors.New("no instances defined")
	}
	if len(config.Instances) > 8 {
		return errors.New("too many instances (maximum 8 allowed)")
	}

	// Track used IDs to check for duplicates
	usedIDs := make(map[string]bool)

	// Validate each instance
	for _, instance := range config.Instances {
		// Validate instance ID for duplicates
		if usedIDs[instance.InstanceId] {
			return errors.New("duplicate instance ID found: " + instance.InstanceId)
		}
		usedIDs[instance.InstanceId] = true

		// Validate instance ID is not empty
		if instance.InstanceId == "" {
			return errors.New("instance ID cannot be empty")
		}

		// Validate program name
		if instance.ProgramName == "" {
			return errors.New("instance " + instance.InstanceId + " has no program name")
		}

		// If instance is enabled, validate its configuration
		if instance.Enabled {
			if len(instance.Periods) == 0 {
				return errors.New("instance " + instance.InstanceId +
					" is enabled but has no periods")
			}
			if len(instance.Periods) > 8 {
				return errors.New("instance " + instance.InstanceId +
					" has too many periods (maximum 8 allowed)")
			}

			// Track used period IDs within this instance
			usedPeriodIDs := make(map[string]bool)

			// Validate each period
			for _, period := range instance.Periods {
				// Validate period ID for duplicates within instance
				if usedPeriodIDs[period.PeriodId] {
					return errors.New("instance " + instance.InstanceId +
						": duplicate period ID found: " + period.PeriodId)
				}
				usedPeriodIDs[period.PeriodId] = true

				// Validate period ID is not empty
				if period.PeriodId == "" {
					return errors.New("instance " + instance.InstanceId +
						": period ID cannot be empty")
				}

				// Validate period name
				if period.Name == "" {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId + " has no name")
				}

				// Validate period status
				validStatuses := map[string]bool{
					"active": true, "inactive": true, "disabled": true,
					"externalNotActive": true, "notUsedOverlap": true,
				}
				if !validStatuses[period.PeriodStatus] {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId +
						" has invalid status: " + period.PeriodStatus)
				}

				// Validate time format (enhanced to support relative times)
				if !IsRelativeTime(period.StartTime) {
					if err := ValidateTimeFormat(period.StartTime); err != nil {
						return errors.New("instance " + instance.InstanceId +
							", period " + period.PeriodId +
							" has invalid start time: " + err.Error())
					}
				}
				if !IsRelativeTime(period.EndTime) {
					if err := ValidateTimeFormat(period.EndTime); err != nil {
						return errors.New("instance " + instance.InstanceId +
							", period " + period.PeriodId +
							" has invalid end time: " + err.Error())
					}
				}

				// Validate active days
				validDays := map[string]bool{
					"monday": true, "tuesday": true, "wednesday": true,
					"thursday": true, "friday": true, "saturday": true, "sunday": true,
				}
				for day := range period.ActiveDays {
					if !validDays[day] {
						return errors.New("instance " + instance.InstanceId +
							", period " + period.PeriodId +
							" has invalid day: " + day)
					}
				}

				// Validate setpoints
				if len(period.Setpoints) == 0 {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId + " has no setpoints")
				}
				if len(period.Setpoints) > 8 {
					return errors.New("instance " + instance.InstanceId +
						", period " + period.PeriodId +
						" has too many setpoints (maximum 8 allowed)")
				}
			}

			// Validate period overlaps within this instance
			if err := ValidateInstancePeriodOverlaps(instance); err != nil {
				return err
			}
		}
	}

	return nil
}

// CreateEnhancedCEBConfig creates an enhanced CEB configuration according to the plan document
func CreateEnhancedCEBConfig() types.CEBConfig {
	return types.CEBConfig{
		ProgramName: "climate energy balance",
		Enabled:     true,

		// Enhanced configuration structure with new Redis key format
		CEBInputRedisKeys: &types.CEBInputRedisKeys{
			ZoneTemperature:                  "hub:h1:io:sensorTemperature",
			BackupZoneTemperature:            "hub:h1:io:backupSensorTemperature",
			ZoneHumidity:                     "hub:h1:io:sensorHumidity",
			ShadePosition:                    "hub:h1:zone:z1:shade:shadePosition",
			CoolingTarget:                    "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
			HeatingTarget:                    "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
			DehumidifyVentilationTarget:      "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget",
			DehumidifyHeatingTarget:          "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget",
			MaxLimitForDehumidifyVentilation: "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent",
			MaxLimitForDehumidifyHeating:     "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat",
			OutdoorTemperature:               "hub:h1:instance:I1:weather:outdoorTemperature",
			LightLevelNI:                     "hub:h1:instance:I1:weather:lightLevelNI",
			LightLevelI:                      "hub:h1:instance:I1:weather:lightLevelI",
		},

		CEBOutputRedisKeys: &types.CEBOutputRedisKeys{
			VentTempControl:          "hub:h1:zone:z1:instance:I1:ceb:ventTempControl",
			VentHumidityControl:      "hub:h1:zone:z1:instance:I1:ceb:ventHumidityControl",
			HighestVentRequest:       "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest",
			SumVentRequests:          "hub:h1:zone:z1:instance:I1:ceb:sumVentRequests",
			HeatTempControl:          "hub:h1:zone:z1:instance:I1:ceb:heatTempControl",
			HeatHumidityControl:      "hub:h1:zone:z1:instance:I1:ceb:heatHumidityControl",
			HighestHeatRequest:       "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest",
			SumHeatRequests:          "hub:h1:zone:z1:instance:I1:ceb:sumHeatRequests",
			HeatingSystemTempRequest: "hub:h1:zone:z1:instance:I1:ceb:heatingSystemTempRequest",
			IntegratedTemp:           "hub:h1:zone:z1:instance:I1:ceb:integratedTemp",
			IntegratedHumidity:       "hub:h1:zone:z1:instance:I1:ceb:integratedHumidity",
		},

		HeatTuning: &types.HeatTuning{
			HeatReqForTemperatureControl: types.HeatReqForTemperatureControl{
				HeatingTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
					CrossModuleReqTime: 30,
				},
				ZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorTemperature",
					CrossModuleReqTime: 30,
				},
				BackupZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:backupSensorTemperature",
					CrossModuleReqTime: 30,
				},
				HeatingProportionalSpanP: 4.0,
				HeatingIntegralTimeI:     50,
			},
			HeatReqForDehumidification: types.HeatReqForDehumidification{
				DehumidificationLimit: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidHeat",
					CrossModuleReqTime: 30,
				},
				CurrentHumidity: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorHumidity",
					CrossModuleReqTime: 30,
				},
				DehumidifyHeatTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyHeatTarget",
					CrossModuleReqTime: 30,
				},
				DehumidifyHeatOffset:            0.0,
				DehumidifyHeatProportionalSpanP: 33.3,
				DehumidifyHeatIntegralTimeI:     30,
			},
			HeatingOutdoorTemperatureEffect: types.HeatingOutdoorTemperatureEffect{
				HeatingTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:heatingTarget",
					CrossModuleReqTime: 30,
				},
				OutdoorTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:instance:I1:weather:outdoorTemperature",
					CrossModuleReqTime: 30,
				},
				ShadePosition: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:shade:shadePosition",
					CrossModuleReqTime: 30,
				},
				ShadeRetractedSettings: types.ShadeSettings{
					MinTempDifference: 0.0,
					MaxTempDifference: 60.0,
					MinOutdoorEffect:  0.0,
					MaxOutdoorEffect:  40.0,
				},
				ShadeExtendedSettings: types.ShadeSettings{
					MinTempDifference: 0.0,
					MaxTempDifference: 60.0,
					MinOutdoorEffect:  0.0,
					MaxOutdoorEffect:  30.0,
				},
			},
			HeatingLightEffect: types.HeatingLightEffect{
				CurrentLightReading: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:instance:I1:weather:lightLevelNI",
					CrossModuleReqTime: 30,
				},
				IntegratedLightReading: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:instance:I1:weather:lightLevelI",
					CrossModuleReqTime: 30,
				},
				LightPredictionModifier: 1.0,
				LightEffectScaling: types.ScalingSettings{
					MinInput:  0.0,
					MaxInput:  1000.0,
					MinOutput: 0.0,
					MaxOutput: 10.0,
				},
			},
			HeatingSystemRequest: types.HeatingSystemRequest{
				RequestNumber:        1,
				UseHigherOrSum:       "higher of the heat requests",
				MinHeatingSystemTemp: 10.0,
				MaxHeatingSystemTemp: 30.0,
			},
		},

		VentilationTuning: &types.VentilationTuning{
			VentReqForTemperatureControl: types.VentReqForTemperatureControl{
				CoolingTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
					CrossModuleReqTime: 30,
				},
				ZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorTemperature",
					CrossModuleReqTime: 30,
				},
				BackupZoneTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:backupSensorTemperature",
					CrossModuleReqTime: 30,
				},
				CoolingProportionalSpanP: 4.0,
				CoolingIntegralTimeI:     50,
			},
			VentReqForDehumidification: types.VentReqForDehumidification{
				DehumidifyVentTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:dehumidifyVentTarget",
					CrossModuleReqTime: 30,
				},
				ZoneHumidity: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:io:sensorHumidity",
					CrossModuleReqTime: 30,
				},
				MaxLimitForDehumidifyVentilation: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:maxDehumidVent",
					CrossModuleReqTime: 30,
				},
				VentilationDehumidifyProportionalSpanP: 33.3,
				IntegralAccumulationTimeI:              30,
			},
			VentilationOutdoorTemperatureEffect: types.VentilationOutdoorTemperatureEffect{
				CoolingTarget: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:zone:z1:instance:I1:setpoint:coolingTarget",
					CrossModuleReqTime: 30,
				},
				OutdoorTemperature: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:instance:I1:weather:outdoorTemperature",
					CrossModuleReqTime: 30,
				},
				VentilationEffectScaling: types.ScalingSettings{
					MinInput:  0.0,
					MaxInput:  60.0,
					MinOutput: 0.0,
					MaxOutput: 1.0,
				},
			},
			VentilationLightEffect: types.VentilationLightEffect{
				CurrentLightReading: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:instance:I1:weather:lightLevelNI",
					CrossModuleReqTime: 30,
				},
				IntegratedLightReading: types.CrossModuleInput{
					Used:               true,
					Address:            "hub:h1:instance:I1:weather:lightLevelI",
					CrossModuleReqTime: 30,
				},
				LightPredictionModifier: 1.0,
				LightEffectScaling: types.ScalingSettings{
					MinInput:  0.0,
					MaxInput:  1000.0,
					MinOutput: 0.0,
					MaxOutput: 1.0,
				},
			},
		},
	}
}

// LoadEnhancedCEBConfig loads the enhanced CEB configuration from file
func LoadEnhancedCEBConfig(filename string) (types.CEBConfig, error) {
	var config types.CEBConfig

	file, err := os.Open(filename)
	if err != nil {
		return config, err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return config, err
	}

	return config, nil
}

// Helper functions using the generic ConfigManager

// LoadDiurnalConfigOnly loads diurnal configuration, returns error if file doesn't exist
func LoadDiurnalConfigOnly(filename string) (types.DiurnalSetpointConfig, error) {
	cm := NewConfigManager(filename, nil, ValidateLoadedConfig)
	return cm.LoadOnly()
}

// LoadClockConfigOnly loads clock configuration, returns error if file doesn't exist
func LoadClockConfigOnly(filename string) (types.ClockConfig, error) {
	cm := NewConfigManager(filename, CreateDefaultClockConfig, nil)
	return cm.LoadOnly()
}

// LoadCEBConfigOnly loads CEB configuration, returns error if file doesn't exist
func LoadCEBConfigOnly(filename string) (types.CEBConfig, error) {
	cm := NewConfigManager(filename, CreateEnhancedCEBConfig, nil)
	return cm.LoadOnly()
}

// SaveConfigGeneric saves any configuration using the generic manager
func SaveConfigGeneric[T any](filename string, config T, validator func(T) error) error {
	cm := NewConfigManager(filename, nil, validator)
	return cm.Save(config)
}
