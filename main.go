package main

import (
	"bufio"
	"context"
	"flag"
	"log"
	"os"
	"time"

	"program-manager/ceb"
	"program-manager/clock"
	"program-manager/config"
	"program-manager/diurnal"
	"program-manager/monitoring"
	"program-manager/program"
	"program-manager/redis"
	"program-manager/types"
)

func main() {
	// Parse command line flags
	var cebVerbose = flag.Bool("ceb-verbose", false, "Enable verbose logging for CEB system (shows detailed logs for 9 outputs)")
	flag.Parse()

	// Initialize Redis client
	redis.Initialize()
	ctx := context.Background()

	// Set up logging
	log.SetFlags(0) // No flags for simplicity
	log.Println("Program started")

	// Clock program starts automatically by default
	log.Println("Clock program starting automatically...")

	// Show list of remaining available programs for user selection
	programs := []string{"diurnalSetpoint", "climateEnergyBalance"}
	log.Println("\nAdditional Programs Available:")
	for i, program := range programs {
		log.Printf("%d. %s", i+1, program)
	}

	// Ask user to select additional programs to enable (comma-separated indices)
	log.Print("Enter the numbers of additional programs to enable (comma-separated, e.g., 1,2) or press Enter for none: ")
	reader := bufio.NewReader(os.Stdin)
	input, err := reader.ReadString('\n')
	if err != nil {
		log.Fatalf("Failed to read input: %v", err)
	}

	// Create program manager with clock enabled by default
	programManager := make(map[string]interface{})
	programManager["clock"] = types.ClockConfig{} // Clock is enabled by default

	// Process user selection for additional programs
	statuses, additionalPrograms, err := program.Selection(programs, input)
	if err != nil {
		log.Fatalf("%v", err)
	}

	// Exit if no programs were selected (empty input)
	if statuses == nil {
		log.Println("No additional programs selected. Exiting...")
		os.Exit(0)
	}

	// Merge additional programs with clock (which is already enabled)
	for key, value := range additionalPrograms {
		programManager[key] = value
	}

	// Create status list including clock as enabled
	allStatuses := []types.ProgramStatus{
		{Name: "clock", Enabled: true},
	}
	if statuses != nil {
		allStatuses = append(allStatuses, statuses...)
	} else {
		// Add disabled status for programs not selected
		for _, program := range programs {
			allStatuses = append(allStatuses, types.ProgramStatus{Name: program, Enabled: false})
		}
	}
	statuses = allStatuses

	// Handle diurnalSetpoint if enabled
	if _, ok := programManager["diurnalSetpoint"].(types.DiurnalSetpointConfig); ok {
		log.Println("\nLoading Enhanced DiurnalSetpoint Configuration...")

		// Load diurnal configuration - return error if file doesn't exist
		diurnalConfig, err := config.LoadDiurnalConfigOnly("config/diurnalConfig.json")
		if err != nil {
			log.Fatalf("Error loading diurnal configuration: %v", err)
		}

		// Validate configuration
		if err := config.ValidateLoadedConfig(diurnalConfig); err != nil {
			log.Fatalf("Invalid diurnal configuration: %v", err)
		}
		log.Println("Enhanced configuration loaded and validated successfully!")

		// Get clock state for relative time processing (if clock is enabled)
		var clockState *types.ClockState
		if _, clockEnabled := programManager["clock"].(types.ClockConfig); clockEnabled {
			// Try to load clock configuration and get current state
			if clockConfig, err := config.LoadClockConfigOnly("config/clockConfig.json"); err == nil {
				tempClockController := clock.NewClockController(clockConfig)
				if err := tempClockController.ProcessCycle(ctx); err == nil {
					clockState = tempClockController.GetCurrentState()
					log.Printf("Diurnal: Using clock state for relative time calculations: Dawn=%s, Dusk=%s",
						clockState.Dawn, clockState.Dusk)
				} else {
					log.Printf("Diurnal: Warning - Failed to get clock state: %v", err)
				}
			} else {
				log.Printf("Diurnal: Warning - Failed to load clock config: %v", err)
			}
		}

		if clockState == nil {
			log.Println("Diurnal: Clock not available, using default dawn/dusk times for relative time calculations")
		}

		// Process diurnal instances with clock state
		diurnal.ProcessInstances(ctx, diurnalConfig, clockState)

		// Start monitoring
		go monitoring.MonitorRampRates(ctx, diurnalConfig, programManager)

		// Print stored setpoints
		if err := redis.PrintAllSetpoints(ctx); err != nil {
			log.Printf("Warning: Failed to print setpoints from Redis: %v", err)
		}

		// Save configuration using generic manager
		if err := config.SaveConfigGeneric("config/diurnalConfig.json", diurnalConfig, config.ValidateLoadedConfig); err != nil {
			log.Fatalf("Failed to save diurnal configuration: %v", err)
		}

		programManager["diurnalSetpoint"] = map[string]bool{"enabled": true}
	}

	// Handle clock if enabled
	var clockController *clock.ClockController
	if _, ok := programManager["clock"].(types.ClockConfig); ok {
		log.Println("\nLoading Clock Program Configuration...")

		// Load clock configuration - return error if file doesn't exist
		clockConfig, err := config.LoadClockConfigOnly("config/clockConfig.json")
		if err != nil {
			log.Fatalf("Failed to load Clock configuration: %v", err)
		}

		// Create and start Clock controller
		clockController = clock.NewClockController(clockConfig)

		// Start Clock processing in a separate goroutine
		go func() {
			// Run immediately on startup
			log.Printf("Clock: Running initial cycle...")
			if err := clockController.ProcessCycle(ctx); err != nil {
				log.Printf("Clock: Error in initial processing cycle: %v", err)
			}

			ticker := time.NewTicker(time.Duration(clockConfig.UpdateInterval) * time.Second)
			defer ticker.Stop()

			log.Printf("Clock: Scheduled to update every %d seconds", clockConfig.UpdateInterval)

			for {
				select {
				case <-ticker.C:
					log.Printf("Clock: Running scheduled update cycle...")
					if err := clockController.ProcessCycle(ctx); err != nil {
						log.Printf("Clock: Error in processing cycle: %v", err)
					}
				case <-ctx.Done():
					log.Printf("Clock: Shutting down...")
					return
				}
			}
		}()

		log.Println("Clock Program started successfully!")

		// Only store enabled status in programManager.json
		programManager["clock"] = map[string]bool{"enabled": true}
	}

	// Handle climateEnergyBalance if enabled
	var cebController *ceb.CEBController
	if _, ok := programManager["climateEnergyBalance"].(types.CEBConfig); ok {
		log.Println("\nLoading Enhanced Climate Energy Balance Configuration...")

		// Load CEB configuration - return error if file doesn't exist
		cebConfig, err := config.LoadCEBConfigOnly("config/cebConfig.json")
		if err != nil {
			log.Fatalf("Failed to load CEB configuration: %v", err)
		}

		// Create and start CEB controller
		cebController = ceb.NewCEBController(cebConfig)

		// Set verbose logging based on command line flag
		cebController.SetVerboseLogging(*cebVerbose)

		// Start CEB processing in a separate goroutine
		go func() {
			ticker := time.NewTicker(5 * time.Second) // Run every 5 seconds
			defer ticker.Stop()

			for {
				select {
				case <-ticker.C:
					if err := cebController.ProcessCycle(ctx); err != nil {
						log.Printf("CEB: Error in processing cycle: %v", err)
					}
				case <-ctx.Done():
					return
				}
			}
		}()

		log.Println("Climate Energy Balance system started successfully!")

		// Only store enabled status in programManager.json
		programManager["climateEnergyBalance"] = map[string]bool{"enabled": true}
	}

	// Write to config/programManager.json
	if err := config.SaveProgramManager("config/programManager.json", programManager); err != nil {
		log.Fatalf("Failed to create config/programManager.json: %v", err)
	}

	log.Printf("config/programManager.json created/updated.\n\n")

	// Print enabled and disabled programs in aligned format
	maxLen := 0
	for _, s := range statuses {
		if l := len(s.Name); l > maxLen {
			maxLen = l
		}
	}
	for _, s := range statuses {
		status := "✗ disabled"
		if s.Enabled {
			status = "✓ enabled"
		}
		log.Printf("%-*s %s", maxLen, s.Name, status)
	}

	// Keep the main program running
	select {}
}
